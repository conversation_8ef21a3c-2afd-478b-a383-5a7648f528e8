package main

import (
	"encoding/json"
	"fmt"
	"github.com/samber/lo"
	"log/slog"
	"math/rand"
	"moyu/pkg/client"
	"strings"
	"time"
)

func randomSleep(min, max time.Duration) {
	time.Sleep(time.Duration(rand.Int63n(int64(max-min))) + min)
}

type Item struct {
	Count int `json:"count"`
}

func main() {
	// Setup structured logging
	var cookie = "Hm_lvt_987895b377fdb91c7d28c2621b2c6b86=**********; HMACCOUNT=96ED20F1C572F91D; nuxt-session=Fe26.2**3eba568f24e0ceb7d3e1a9cb19a3b5f940501f6f3f311aea9efa0951e19710da*8J8P_QTjpBIyUGrGgz0jYw*-wc5P8qc7vUr31BVYZlDAiPg9UOpaeOWbusU2jliSQFknAmWPyiQuEEFkOa8yQNqtdCh-P8q-2QOGSKPlp9rfiJVZ7s-Qhl8dorY5G9nezPZ_86ysQmZ_29N0xhe1_YxmJap5o9uckPQs76JSiEHQ4P2WT7UabS4tYACGSLVH6Qy19Qgjcv3sYG-MS_84RA9ncTc8Fe6Zb5EpZY75uO33VDCXLpR9LspZiwmzxguAxpMsi7X1sLXszDLAvY4WWxG-LpT43O3WDitby9R7JecwSmpfAY-sQRMIgbDH-Ynnds***************c92adb2ddb634c9b2fa4b053bd531185760688e5c5a8a971901ede6fbd847de8*yY-sBQcIa2bBFFCMhhfJ-x4ZFSIV98Wgbc-X49ckEtw; Hm_lpvt_987895b377fdb91c7d28c2621b2c6b86=**********"
	var enhanceItem = "axe"
	var maxEnhanceLevel = "+5"
	c, err := client.New("https://www.moyu-idle.com", cookie)
	if err != nil {
		slog.Error("创建客户端失败", slog.String("error", err.Error()))
		return
	}
	slog.SetLogLoggerLevel(slog.LevelInfo)

	// Enable auto-reconnect with 5 second interval and max 10 attempts
	c.EnableAutoReconnect(5*time.Second, 10000)

	if err := c.Connect(); err != nil {
		slog.Error("连接失败", slog.String("error", err.Error()))
		return
	}
	var events = map[string]bool{
		"enhance:require:success": true,
		"inventoryUpdated":        true,
	}
	var itemMap = map[string]int{}

	go func() {
		for {
			time.Sleep(100 * time.Millisecond)
			if c.Opened {
				break
			}
		}

		f := func(itemMap map[string]int) {
			time.Sleep(1 * time.Second)
			if len(itemMap) == 0 {
				slog.Info("没有可强化的物品")
				return
			}

			var first = lo.Keys(itemMap)[0]
			if first == enhanceItem+maxEnhanceLevel {
				fmt.Println("已经强化到最大等级,跳过: ", first)
			}
			fmt.Println("开始强化:" + first)
			if err := c.EnhanceRequire(first); err != nil {
				slog.Error("发送合成请求失败", slog.String("error", err.Error()), c.AttrUserName())
			}
		}
		go func() {
			for {
				randomSleep(1*time.Second, 3*time.Second)
				f(itemMap)
			}
		}()

		c.OnEventHandle = func(event string, val []any) {
			if !events[event] {
				return
			}
			data := val[0].(map[string]any)["data"]

			if event == "inventoryUpdated" {
				var newItemMap = map[string]int{}
				var sourceMap map[string]Item
				if err := cast(data, &sourceMap); err != nil {
					panic(err)
				}
				for key, item := range sourceMap {
					if key == "wood" || key == "stone" {
						if item.Count < 50 {
							panic("木头或石头数量为0,请检查是否有足够的资源进行强化")
						}
					}
					if strings.HasPrefix(key, enhanceItem) {
						if fmt.Sprintf("%s%s", enhanceItem, maxEnhanceLevel) != key && item.Count > 0 {
							newItemMap[key] = item.Count
						}
					}
				}
				itemMap = newItemMap
			}
			if event == "enhance:require:success" {
				m := data.(map[string]any)
				fmt.Printf("%s %s\n", m["enhanceResultId"], m["msg"])
			}

		}

	}()
	select {}
}

func cast[T any](source any, target *T) error {
	bytes, err := json.Marshal(source)
	if err != nil {
		return err
	}
	return json.Unmarshal(bytes, target)
}
