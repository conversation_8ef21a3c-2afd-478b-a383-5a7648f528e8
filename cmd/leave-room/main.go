package main

import (
	"log/slog"
	"math/rand"
	"moyu/pkg/client"
	"os"
	"time"
)

func randomSleep(min, max time.Duration) {
	time.Sleep(time.Duration(rand.Int63n(int64(max-min))) + min)
}

type Item struct {
	Count int `json:"count"`
}

func main() {
	// Setup structured logging
	s := os.Args[1]
	c, err := client.New("https://www.moyu-idle.com", s)
	if err != nil {
		slog.Error("创建客户端失败", slog.String("error", err.Error()))
		return
	}

	slog.SetLogLoggerLevel(slog.LevelDebug)

	// Enable auto-reconnect with 5 second interval and max 10 attempts
	c.EnableAutoReconnect(5*time.Second, 10000)

	if err := c.Connect(); err != nil {
		slog.Error("连接失败", slog.String("error", err.<PERSON>rror()))
		return
	}

	go func() {
		for {
			time.Sleep(100 * time.Millisecond)
			if c.Opened {
				break
			}
		}

		go func() {

		}()

		c.OnEventHandle = func(event string, val []any) {

		}
		for i := 0; i < 10; i++ {
			c.BattleRoomLeave()
		}
		os.Exit(0)

	}()
	select {}
}
